/**
 * <PERSON><PERSON><PERSON> for copying phone numbers to clipboard
 * This script adds click event listeners to all elements with class 'copy-number'
 * When clicked, it copies the phone number to the clipboard and shows feedback
 */
document.addEventListener('DOMContentLoaded', function() {
    // Add click event listeners to all elements with class 'copy-number'
    const phoneNumbers = document.querySelectorAll('.copy-number');

    phoneNumbers.forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the phone number from the data attribute
            const phoneNumber = this.getAttribute('data-number');

            // Create a temporary input element
            const tempInput = document.createElement('input');
            tempInput.value = phoneNumber;
            document.body.appendChild(tempInput);

            // Select and copy the text
            tempInput.select();
            document.execCommand('copy');

            // Remove the temporary element
            document.body.removeChild(tempInput);

            // Show feedback to the user
            const originalText = this.textContent;
            this.textContent = 'Copied!';

            // Reset the text after a short delay
            setTimeout(() => {
                this.textContent = originalText;
            }, 1500);
        });
    });
});
