<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Improved Experience Badge</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #F4F2EF;
            color: #272525;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* Container for the experience section */
        .experience-container {
            max-width: 900px;
            width: 100%;
        }

        /* Experience card styles */
        .experience-card {
            background-color: #fff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08);
            display: grid;
            grid-template-columns: 1fr 2fr;
            margin-bottom: 40px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .experience-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
        }

        /* Header section with badge */
        .card-header {
            padding: 0;
            display: contents;
        }

        /* Experience badge */
        .experience-badge {
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #336021, #2a4e1c);
            color: #fff;
            padding: 40px 20px;
            box-shadow: 0 10px 25px rgba(51, 96, 33, 0.3);
            overflow: hidden;
        }

        .experience-badge::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                to bottom right,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 40%,
                rgba(255, 255, 255, 0) 60%
            );
            transform: rotate(-45deg);
            pointer-events: none;
        }

        .experience-badge .years {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 5px;
            background: linear-gradient(135deg, #fff, #E68C3A);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .experience-badge .text {
            font-size: 1.1rem;
            font-weight: 500;
            text-align: center;
            line-height: 1.3;
        }

        /* Card content */
        .card-content {
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .card-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 15px;
            color: #336021;
        }

        .card-description {
            font-size: 1rem;
            line-height: 1.6;
            color: #666;
            margin-bottom: 25px;
        }

        /* Features list */
        .features-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .feature-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            background-color: rgba(230, 140, 58, 0.15);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .feature-icon svg {
            width: 20px;
            height: 20px;
            fill: #E68C3A;
        }

        .feature-text {
            font-size: 1rem;
            color: #444;
        }

        /* Call to action button */
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #E68C3A, #d67b29);
            color: #fff;
            padding: 16px 38px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 10px 25px rgba(230, 140, 58, 0.3);
            margin-top: 20px;
        }

        .cta-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(230, 140, 58, 0.4);
            background: linear-gradient(135deg, #336021, #2a4e1c);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .experience-card {
                grid-template-columns: 1fr;
            }

            .experience-badge {
                padding: 30px 20px;
            }

            .card-content {
                padding: 30px;
            }

            .features-list {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .card-title {
                font-size: 1.5rem;
                margin-top: 10px;
            }
        }

        @media (max-width: 480px) {
            .experience-badge .years {
                font-size: 3rem;
            }

            .experience-badge .text {
                font-size: 1rem;
            }

            .card-content {
                padding: 25px 20px;
            }

            .cta-button {
                width: 100%;
                text-align: center;
                padding: 14px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="experience-container">
        <div class="experience-card">
            <div class="experience-badge">
                <div class="years">13+</div>
                <div class="text">Years Work Experience</div>
            </div>
            <div class="card-content">
                <h2 class="card-title">Professional Expertise</h2>
                <p class="card-description">With over 13 years of industry experience, we deliver exceptional results through our refined processes and attention to detail. Our expertise ensures a cleaner and more efficient approach to every project.</p>

                <div class="features-list">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                        </div>
                        <div class="feature-text">Professional-grade equipment</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                        </div>
                        <div class="feature-text">Eco-friendly solutions</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                        </div>
                        <div class="feature-text">Attention to detail</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                        </div>
                        <div class="feature-text">Consistent quality</div>
                    </div>
                </div>

                <a href="#" class="cta-button">Learn More</a>
            </div>
        </div>
    </div>
</body>
</html>
