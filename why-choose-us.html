<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Why Choose Us - Improved UI</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            /* Main colors from index.html */
            --primary-color: #336021;
            --secondary-color: #E68C3A;
            --dark-color: #272525;
            --light-color: #F4F2EF;
            --white-color: #FFFFFF;
            --gray-color: #666666;

            /* Lighter and darker variations */
            --primary-light: #3d7228;
            --primary-dark: #294d1a;
            --secondary-light: #eb9d57;
            --secondary-dark: #d47a29;

            /* Transparent variations */
            --primary-10: rgba(51, 96, 33, 0.1);
            --primary-20: rgba(51, 96, 33, 0.2);
            --primary-50: rgba(51, 96, 33, 0.5);
            --secondary-10: rgba(230, 140, 58, 0.1);
            --secondary-20: rgba(230, 140, 58, 0.2);

            /* Text colors */
            --text-dark: var(--dark-color);
            --text-light: var(--gray-color);

            /* UI Elements */
            --border-radius: 16px;
            --shadow-sm: 0 5px 15px rgba(0,0,0,0.08);
            --shadow-md: 0 10px 30px rgba(0,0,0,0.12);
            --shadow-lg: 0 15px 35px rgba(0,0,0,0.15);

            /* Transitions */
            --transition-fast: all 0.2s ease;
            --transition-normal: all 0.3s ease;
            --transition-slow: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light-color);
            color: var(--text-dark);
            line-height: 1.6;
            padding: 40px 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        .section-header {
            text-align: center;
            margin-bottom: 60px;
            position: relative;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 20px;
            position: relative;
            display: inline-block;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%) skewX(-30deg);
            width: 80px;
            height: 4px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
        }

        .section-description {
            max-width: 800px;
            margin: 0 auto;
            font-size: 1.1rem;
            color: var(--text-light);
            padding-top: 10px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .feature-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            transition: var(--transition-slow);
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
            border-bottom: 4px solid transparent;
        }

        .feature-card:hover {
            transform: translateY(-15px) scale(1.03);
            box-shadow: var(--shadow-lg);
            z-index: 2;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-10), transparent);
            opacity: 0;
            transition: var(--transition-normal);
            z-index: -1;
        }

        .feature-card:nth-child(even)::before {
            background: linear-gradient(135deg, var(--secondary-10), transparent);
        }

        .feature-card:hover::before {
            opacity: 1;
        }

        .feature-card:hover {
            border-bottom: 4px solid var(--primary-color);
        }

        .feature-card:nth-child(even):hover {
            border-bottom: 4px solid var(--secondary-color);
        }

        .feature-icon-wrapper {
            width: 100%;
            display: flex;
            justify-content: center;
            padding: 30px 0 15px;
            position: relative;
        }

        .feature-icon {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--primary-10);
            margin-bottom: 20px;
            transition: var(--transition-slow);
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .feature-card:nth-child(even) .feature-icon {
            background-color: var(--secondary-10);
        }

        .feature-icon img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
            transition: var(--transition-normal);
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.15) rotate(0deg);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .feature-icon::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255,255,255,0.2), transparent);
            opacity: 0;
            transition: var(--transition-normal);
        }

        .feature-card:hover .feature-icon::after {
            opacity: 1;
            transform: rotate(120deg);
        }

        .feature-content {
            padding: 0 30px 30px;
            text-align: center;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 1;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-color);
            transition: var(--transition-normal);
            position: relative;
            display: inline-block;
            align-self: center;
        }

        .feature-card:nth-child(even) .feature-title {
            color: var(--secondary-color);
        }

        .feature-card:hover .feature-title {
            transform: translateY(-5px);
        }

        .feature-description {
            color: var(--text-light);
            font-size: 1rem;
            line-height: 1.7;
            flex-grow: 1;
            transition: var(--transition-normal);
        }

        .feature-card:hover .feature-description {
            color: var(--text-dark);
        }

        .feature-link {
            margin-top: 25px;
            display: inline-block;
            color: var(--primary-color);
            font-weight: 500;
            text-decoration: none;
            position: relative;
            transition: var(--transition-normal);
            padding: 8px 20px;
            border-radius: 30px;
            overflow: hidden;
            background-color: transparent;
            border: 1px solid var(--primary-10);
        }

        .feature-card:nth-child(even) .feature-link {
            color: var(--secondary-color);
            border: 1px solid var(--secondary-10);
        }

        .feature-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--primary-10);
            transform: scaleX(0);
            transform-origin: right;
            transition: transform 0.5s cubic-bezier(0.86, 0, 0.07, 1);
            z-index: -1;
        }

        .feature-card:nth-child(even) .feature-link::before {
            background-color: var(--secondary-10);
        }

        .feature-link:hover {
            color: var(--primary-dark);
            border-color: var(--primary-color);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .feature-card:nth-child(even) .feature-link:hover {
            color: var(--secondary-dark);
            border-color: var(--secondary-color);
        }

        .feature-link:hover::before {
            transform: scaleX(1);
            transform-origin: left;
        }

        .feature-link::after {
            content: '→';
            margin-left: 5px;
            transition: var(--transition-normal);
            opacity: 0;
            transform: translateX(-10px);
        }

        .feature-link:hover::after {
            opacity: 1;
            transform: translateX(3px);
        }

        @media (max-width: 992px) {
            .features-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .section-title {
                font-size: 2rem;
            }

            .section-description {
                font-size: 1rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
                max-width: 500px;
                margin-left: auto;
                margin-right: auto;
            }
        }

        @media (max-width: 480px) {
            .section-title {
                font-size: 1.8rem;
            }

            .feature-content {
                padding: 0 20px 20px;
            }

            .feature-title {
                font-size: 1.3rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Why Choose Us?</h2>
            <p class="section-description">We stand out in the renewable energy industry with our commitment to excellence, innovation, and customer satisfaction.</p>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon-wrapper">
                    <div class="feature-icon">
                        <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80" alt="Experienced Team">
                    </div>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">Experienced Team</h3>
                    <p class="feature-description">Our experts in solar energy and renewable technologies bring years of industry experience to every project we undertake.</p>
                    <a href="#" class="feature-link">Learn More</a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon-wrapper">
                    <div class="feature-icon">
                        <img src="https://images.unsplash.com/photo-1573497620053-ea5300f94f21?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80" alt="Customer Service">
                    </div>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">Customer Service</h3>
                    <p class="feature-description">We prioritize client needs with 24/7 support and personalized attention throughout your renewable energy journey.</p>
                    <a href="#" class="feature-link">Learn More</a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon-wrapper">
                    <div class="feature-icon">
                        <img src="https://images.unsplash.com/photo-1497032628192-86f99bcd76bc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80" alt="Innovative Solutions">
                    </div>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">Innovative Solutions</h3>
                    <p class="feature-description">We develop cutting-edge renewable energy solutions that are both efficient and environmentally responsible.</p>
                    <a href="#" class="feature-link">Learn More</a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon-wrapper">
                    <div class="feature-icon">
                        <img src="https://images.unsplash.com/photo-1551836022-d5d88e9218df?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80" alt="Quality Service">
                    </div>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">Quality Service</h3>
                    <p class="feature-description">Our commitment to delivering high-standard results ensures that we consistently exceed client expectations.</p>
                    <a href="#" class="feature-link">Learn More</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
