#!/bin/bash

# <PERSON><PERSON>t to update contact information across all HTML files

# Define the new contact information
NEW_PHONE="9633126288"
OLD_PHONE="+35351349288"
NEW_EMAIL="<EMAIL>"
OLD_EMAIL="<EMAIL>"
NEW_ADDRESS="No 27 New, MGR Main Rd, Kandhanchavadi, Perungudi, Chennai, Tamil Nadu 600096, India"

# Find all HTML files
HTML_FILES=$(find . -name "*.html")

# Loop through each HTML file
for file in $HTML_FILES; do
    echo "Processing $file..."
    
    # Replace phone numbers
    sed -i "s|data-number=\"$OLD_PHONE\"|data-number=\"$NEW_PHONE\"|g" "$file"
    sed -i "s|$OLD_PHONE|$NEW_PHONE|g" "$file"
    
    # Replace email addresses
    sed -i "s|to=$OLD_EMAIL|to=$NEW_EMAIL|g" "$file"
    sed -i "s|$OLD_EMAIL|$NEW_EMAIL|g" "$file"
    
    # Add address if needed (this is more complex and might need manual intervention)
    # We'll check for specific patterns and add the address
    
    echo "Updated $file"
done

echo "Contact information update complete!"
