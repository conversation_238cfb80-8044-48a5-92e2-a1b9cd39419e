/**
 * Modern Design Enhancements for Solar Energy Website
 * Adds smooth animations, scroll effects, and interactive elements
 */

(function() {
    'use strict';

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        initScrollReveal();
        initSmoothScrolling();
        initParallaxEffects();
        initModernInteractions();
        initLoadingAnimations();
        initCounterAnimations();
        initFormEnhancements();
    });

    /**
     * Scroll Reveal Animation
     */
    function initScrollReveal() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('active');
                }
            });
        }, observerOptions);

        // Add reveal class to elements
        const revealElements = document.querySelectorAll('.feature-card, .modern-card, .section-header, .banner-main-wrapper-one');
        revealElements.forEach(el => {
            el.classList.add('reveal');
            observer.observe(el);
        });
    }

    /**
     * Smooth Scrolling for Anchor Links
     */
    function initSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    /**
     * Parallax Effects for Background Elements
     */
    function initParallaxEffects() {
        const parallaxElements = document.querySelectorAll('.banner-area, .cta-area');
        
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;

            parallaxElements.forEach(element => {
                element.style.transform = `translateY(${rate}px)`;
            });
        });
    }

    /**
     * Modern Interactive Elements
     */
    function initModernInteractions() {
        // Enhanced button hover effects
        const buttons = document.querySelectorAll('.rts-btn, .btn-modern');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Card hover effects
        const cards = document.querySelectorAll('.feature-card, .modern-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Add ripple effect to buttons
        addRippleEffect();
    }

    /**
     * Add Ripple Effect to Buttons
     */
    function addRippleEffect() {
        const buttons = document.querySelectorAll('.rts-btn, .btn-modern');
        
        buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');
                
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    }

    /**
     * Loading Animations
     */
    function initLoadingAnimations() {
        // Stagger animation for cards
        const cards = document.querySelectorAll('.feature-card, .service-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('animate-fade-in-up');
        });

        // Text animation for headings
        const headings = document.querySelectorAll('h1, h2, .banner-title');
        headings.forEach((heading, index) => {
            heading.style.animationDelay = `${index * 0.2}s`;
            heading.classList.add('animate-fade-in-left');
        });
    }

    /**
     * Counter Animations
     */
    function initCounterAnimations() {
        const counters = document.querySelectorAll('.counter-number, .funfact-number');
        
        const counterObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter(entry.target);
                }
            });
        }, { threshold: 0.5 });

        counters.forEach(counter => {
            counterObserver.observe(counter);
        });
    }

    /**
     * Animate Counter Numbers
     */
    function animateCounter(element) {
        const target = parseInt(element.textContent);
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 16);
    }

    /**
     * Form Enhancements
     */
    function initFormEnhancements() {
        const inputs = document.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            // Add focus effects
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });

            // Add floating labels
            if (input.value) {
                input.parentElement.classList.add('focused');
            }
        });

        // Form validation feedback
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.classList.add('error');
                        isValid = false;
                    } else {
                        field.classList.remove('error');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    showNotification('Please fill in all required fields', 'error');
                }
            });
        });
    }

    /**
     * Show Notification
     */
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    /**
     * Add CSS for animations and effects
     */
    function addDynamicStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple-animation 0.6s linear;
                pointer-events: none;
            }
            
            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
            
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                z-index: 10000;
            }
            
            .notification.show {
                transform: translateX(0);
            }
            
            .notification-info {
                background: #3B82F6;
            }
            
            .notification-error {
                background: #EF4444;
            }
            
            .notification-success {
                background: #10B981;
            }
            
            .form-group.focused label {
                transform: translateY(-20px) scale(0.8);
                color: var(--color-primary);
            }
            
            .form-control.error {
                border-color: #EF4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }
        `;
        document.head.appendChild(style);
    }

    // Add dynamic styles
    addDynamicStyles();

})();
