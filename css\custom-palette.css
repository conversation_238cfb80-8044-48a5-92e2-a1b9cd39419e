/* Custom Color Palette CSS */
:root {
    /* New color palette */
    --blue-dianne: #264653;
    --jungle-green: #2A9D8F;
    --rob-roy: #E9C46A;
    --sandy-brown: #F4A261;
    --burnt-sienna: #E76F51;

    /* Lighter and darker variations */
    --blue-dianne-light: #335b6b;
    --blue-dianne-dark: #1a323c;
    --jungle-green-light: #34b8a8;
    --jungle-green-dark: #1f7268;
    --rob-roy-light: #f0d285;
    --rob-roy-dark: #d4b052;
    --sandy-brown-light: #f7b57f;
    --sandy-brown-dark: #e08743;
    --burnt-sienna-light: #ec8a70;
    --burnt-sienna-dark: #d05438;

    /* Transparent variations for overlays and backgrounds */
    --blue-dianne-10: rgba(38, 70, 83, 0.1);
    --blue-dianne-20: rgba(38, 70, 83, 0.2);
    --blue-dianne-50: rgba(38, 70, 83, 0.5);
    --blue-dianne-80: rgba(38, 70, 83, 0.8);
    --jungle-green-10: rgba(42, 157, 143, 0.1);
    --jungle-green-20: rgba(42, 157, 143, 0.2);
    --jungle-green-50: rgba(42, 157, 143, 0.5);
    --rob-roy-10: rgba(233, 196, 106, 0.1);
    --rob-roy-20: rgba(233, 196, 106, 0.2);
    --sandy-brown-10: rgba(244, 162, 97, 0.1);
    --sandy-brown-20: rgba(244, 162, 97, 0.2);
    --burnt-sienna-10: rgba(231, 111, 81, 0.1);
    --burnt-sienna-20: rgba(231, 111, 81, 0.2);

    /* Text colors */
    --text-dark: var(--blue-dianne-dark);
    --text-light: #ffffff;
    --text-muted: #6c757d;

    /* Functional colors */
    --primary: var(--jungle-green);
    --secondary: var(--blue-dianne);
    --accent: var(--burnt-sienna);
    --highlight: var(--rob-roy);
    --neutral: var(--sandy-brown);

    /* UI Elements */
    --background-light: #f8f9fa;
    --background-dark: var(--blue-dianne);
    --border-color: #e9ecef;
    --shadow-sm: 0 .125rem .25rem rgba(0,0,0,.075);
    --shadow-md: 0 .5rem 1rem rgba(0,0,0,.15);
    --shadow-lg: 0 1rem 3rem rgba(0,0,0,.175);

    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* Apply new color palette to existing elements */
body {
    color: var(--text-dark);
}

.header-four {
    background-color: var(--blue-dianne);
}

.header-four .nav-link {
    color: var(--text-light) !important;
}

.header-four .nav-link::after {
    background-color: var(--burnt-sienna);
}

.header-four .contact {
    background-color: var(--jungle-green);
    color: var(--text-light);
    transition: var(--transition-normal);
}

.header-four .contact:hover {
    background-color: var(--burnt-sienna);
    transform: translateY(-3px);
}

/* Banner area styling */
.banner-overlay {
    background: transparent !important; /* Completely removed overlay to make it transparent */
}

.banner-tag {
    background-color: var(--jungle-green-20) !important;
    color: var(--text-light) !important;
}

.under-line-button {
    background-color: var(--burnt-sienna) !important;
    box-shadow: 0 5px 15px var(--burnt-sienna-20) !important;
}

.under-line-button:hover {
    background-color: var(--jungle-green) !important;
    box-shadow: 0 10px 25px var(--jungle-green-20) !important;
}

.ghost-button {
    border-color: var(--text-light) !important;
}

.ghost-button:hover {
    background-color: var(--jungle-green) !important;
    border-color: var(--jungle-green) !important;
    color: var(--text-light) !important;
}

.social-wrapper-one-horizental a {
    background-color: var(--blue-dianne-20) !important;
}

.social-wrapper-one-horizental a:hover {
    background-color: var(--burnt-sienna) !important;
}

/* About section styling */
.section-tag {
    background-color: var(--rob-roy-10) !important;
    color: var(--rob-roy-dark) !important;
}

.title.skew-up::after {
    background: linear-gradient(to right, var(--jungle-green), var(--burnt-sienna)) !important;
}

.engineer-experience-area {
    border-left: 4px solid var(--jungle-green) !important;
}

.engineer-experience-area .authora-area img {
    border-color: var(--jungle-green) !important;
}

.engineer-experience-area .infor-mation span {
    color: var(--jungle-green) !important;
}

.experience-area {
    background: linear-gradient(135deg, var(--jungle-green), var(--jungle-green-dark)) !important;
}

.key-point .icon {
    background-color: var(--rob-roy-10) !important;
}

.key-point .icon i {
    color: var(--jungle-green) !important;
}

.button-circle-text {
    color: var(--jungle-green) !important;
}

.button-circle-text:hover {
    color: var(--burnt-sienna) !important;
}

/* Service cards styling */
.single-service-area-6 {
    border: 1px solid var(--border-color);
    transition: var(--transition-normal);
}

.single-service-area-6:hover {
    border-color: var(--jungle-green-light);
}

.single-service-area-6 .inner-content .icon {
    background-color: var(--jungle-green-10) !important;
}

.single-service-area-6 .inner-content span {
    color: var(--jungle-green) !important;
}

.single-service-area-6 .read-more {
    color: var(--burnt-sienna) !important;
}

/* Counter section styling */
.single-counter-up-start-solari {
    border-bottom: 3px solid var(--jungle-green) !important;
}

.single-counter-up-start-solari .bg-text {
    color: var(--jungle-green-10) !important;
}

.single-counter-up-start-solari .title {
    color: var(--blue-dianne) !important;
}

/* Project section styling */
.single-project-area-start {
    border: 1px solid var(--border-color);
}

.single-project-area-start .tag {
    background-color: var(--rob-roy) !important;
    color: var(--blue-dianne) !important;
}

/* FAQ section styling */
.accordion-button:not(.collapsed) {
    background-color: var(--jungle-green) !important;
}

.accordion-button:focus {
    border-color: var(--jungle-green-light) !important;
    box-shadow: 0 0 0 0.25rem var(--jungle-green-20) !important;
}

/* Contact form styling */
.appoinment-area-six-wrapper form .single-input-area input:focus,
.appoinment-area-six-wrapper form .single-input-area textarea:focus {
    border-color: var(--jungle-green) !important;
    box-shadow: 0 0 0 3px var(--jungle-green-10) !important;
}

.rts-btn.btn-primary {
    background: linear-gradient(135deg, var(--jungle-green), var(--jungle-green-dark)) !important;
}

.rts-btn.btn-primary:hover {
    background: linear-gradient(135deg, var(--burnt-sienna), var(--jungle-green)) !important;
}

/* Footer styling */
.rts-footer-one {
    background-color: var(--blue-dianne) !important;
}

.rts-footer-one::before {
    background: linear-gradient(to right, var(--jungle-green), var(--burnt-sienna)) !important;
}

.rts-footer-one .title {
    color: var(--text-light) !important;
}

.rts-footer-one p,
.rts-footer-one a {
    color: rgba(255, 255, 255, 0.8) !important;
}

.rts-footer-one a:hover {
    color: var(--rob-roy) !important;
}

/* Testimonial section styling */
.testimonial-section {
    background-color: var(--blue-dianne-10);
}

.testimonial-card {
    background-color: var(--text-light);
    border-radius: 10px;
    box-shadow: var(--shadow-md);
    border-bottom: 3px solid var(--jungle-green);
}

.testimonial-card .quote-icon {
    color: var(--rob-roy);
}

.testimonial-card .client-info .name {
    color: var(--blue-dianne);
}

.testimonial-card .client-info .position {
    color: var(--jungle-green);
}

/* Newsletter section styling */
.newsletter-section {
    background: linear-gradient(135deg, var(--blue-dianne), var(--jungle-green));
}

.newsletter-form input {
    border: none;
    border-radius: 50px;
    padding: 15px 25px;
}

.newsletter-form button {
    background-color: var(--burnt-sienna);
    border: none;
    border-radius: 50px;
    color: var(--text-light);
    padding: 15px 30px;
    font-weight: 600;
    transition: var(--transition-normal);
}

.newsletter-form button:hover {
    background-color: var(--burnt-sienna-dark);
    transform: translateY(-3px);
}

/* Why Choose Us section styling */
.why-choose-card {
    border-radius: 10px;
    background-color: var(--text-light);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    border-bottom: 3px solid transparent;
}

.why-choose-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md);
    border-bottom-color: var(--jungle-green);
}

.why-choose-card .icon {
    background-color: var(--jungle-green-10);
    color: var(--jungle-green);
}

.why-choose-card:nth-child(2) .icon {
    background-color: var(--rob-roy-10);
    color: var(--rob-roy-dark);
}

.why-choose-card:nth-child(3) .icon {
    background-color: var(--burnt-sienna-10);
    color: var(--burnt-sienna);
}

.why-choose-card:nth-child(4) .icon {
    background-color: var(--sandy-brown-10);
    color: var(--sandy-brown-dark);
}

/* Stats counter styling */
.stats-counter {
    background: linear-gradient(135deg, var(--blue-dianne), var(--jungle-green));
    color: var(--text-light);
}

/* Animation keyframes */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.float-animation {
    animation: float 3s ease-in-out infinite;
}