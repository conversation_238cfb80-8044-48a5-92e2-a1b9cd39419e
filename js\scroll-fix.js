/**
 * Fix for scroll issues in aboutus.html
 * This script disables ScrollTrigger functionality that might be causing the page to reset to top
 */

(function() {
    // Wait for page to fully load
    window.addEventListener('load', function() {
        // Function to disable ScrollTrigger
        function disableScrollTrigger() {
            if (typeof ScrollTrigger !== 'undefined') {
                try {
                    // Disable all ScrollTrigger instances
                    var triggers = ScrollTrigger.getAll();
                    triggers.forEach(function(trigger) {
                        trigger.kill();
                    });
                    
                    // Override ScrollTrigger methods to prevent new instances
                    var originalCreate = ScrollTrigger.create;
                    ScrollTrigger.create = function(config) {
                        console.log("ScrollTrigger creation prevented:", config);
                        return {
                            kill: function() {},
                            disable: function() {},
                            enable: function() {},
                            scroll: function() {}
                        };
                    };
                    
                    console.log("ScrollTrigger disabled successfully");
                } catch(e) {
                    console.log("Error disabling ScrollTrigger:", e);
                }
            }
        }
        
        // Function to maintain scroll position
        function maintainScrollPosition() {
            var lastScrollPosition = window.scrollY;
            
            // Check scroll position every 100ms
            setInterval(function() {
                // If page has unexpectedly scrolled to top, restore position
                if (window.scrollY < 10 && lastScrollPosition > 20) {
                    window.scrollTo(0, lastScrollPosition);
                    console.log("Scroll position restored to:", lastScrollPosition);
                } else {
                    // Update last known scroll position
                    lastScrollPosition = window.scrollY;
                }
            }, 100);
            
            // Also track scroll events
            window.addEventListener('scroll', function() {
                lastScrollPosition = window.scrollY;
            });
        }
        
        // Execute fixes
        disableScrollTrigger();
        maintainScrollPosition();
        
        // Also disable smooth scrolling
        document.documentElement.style.scrollBehavior = 'auto';
        document.body.style.scrollBehavior = 'auto';
        
        // Remove all animation classes
        document.querySelectorAll('.skew-up, .reveal, .animate__animated').forEach(function(el) {
            el.style.opacity = '1';
            el.style.transform = 'none';
            el.style.transition = 'none';
            el.classList.remove('skew-up', 'reveal', 'animate__animated');
        });
    });
})();
