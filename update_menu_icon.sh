#!/bin/bash

# Files to update
files=("./aboutus.html" "./index.html" "./service.html" "./thank-you.html")

# New SVG content for hamburger menu
new_svg='<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect y="4" width="24" height="3" rx="1.5" fill="white"></rect>
                                    <rect y="11" width="24" height="3" rx="1.5" fill="white"></rect>
                                    <rect y="18" width="24" height="3" rx="1.5" fill="white"></rect>
                                </svg>'

# Loop through each file
for file in "${files[@]}"; do
  echo "Processing $file..."
  
  # Use sed to replace the SVG content
  sed -i -E 's|<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">(\s+)<rect width="6" height="6" fill="white"></rect>(\s+)<rect y="9" width="6" height="6" fill="white"></rect>(\s+)<rect y="18" width="6" height="6" fill="white"></rect>(\s+)<rect x="9" width="6" height="6" fill="white"></rect>(\s+)<rect x="9" y="9" width="6" height="6" fill="white"></rect>(\s+)<rect x="9" y="18" width="6" height="6" fill="white"></rect>(\s+)<rect x="18" width="6" height="6" fill="white"></rect>(\s+)<rect x="18" y="9" width="6" height="6" fill="white"></rect>(\s+)<rect x="18" y="18" width="6" height="6" fill="white"></rect>|'"$new_svg"'|g' "$file"
  
  echo "Updated $file"
done

echo "All files updated successfully!"
