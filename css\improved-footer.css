/* === Improved Footer Styles === */

/* --- Root Variables for easy customization --- */
:root {
    --footer-bg: #1a1a1a;
    --footer-text-color: #b0b0b0;
    --footer-heading-color: #ffffff;
    --footer-link-color: #b0b0b0;
    --footer-link-hover-color: var(--primary-color);
    --footer-border-color: rgba(255, 255, 255, 0.1);
    --footer-social-icon-bg: rgba(255, 255, 255, 0.08);
    --footer-social-icon-hover-bg: var(--primary-color);
    --footer-social-icon-hover-color: #ffffff;
}

/* --- Main Footer Container --- */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text-color);
    font-family: 'Poppins', sans-serif;
    line-height: 1.7;
    font-size: 15px;
    position: relative;
    border-top: 4px solid var(--primary-color);
}

/* --- Footer Top Section --- */
.footer-top {
    padding: 80px 0;
}

/* --- Footer Widget --- */
.footer-widget {
    margin-bottom: 40px;
}

/* --- About Widget --- */
.about-widget .footer-logo {
    max-width: 160px;
    margin-bottom: 20px;
}

.about-widget p {
    margin-bottom: 25px;
}

/* --- Social Links --- */
.social-links {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background-color: var(--footer-social-icon-bg);
    color: var(--footer-text-color);
    font-size: 16px;
    transition: all 0.3s ease-in-out;
    border: 1px solid var(--footer-border-color);
}

.social-links a:hover {
    background-color: var(--footer-social-icon-hover-bg);
    color: var(--footer-social-icon-hover-color);
    transform: translateY(-4px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

/* --- Footer Titles --- */
.footer-title {
    color: var(--footer-heading-color);
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 30px;
    position: relative;
    padding-bottom: 15px;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

/* --- Footer Links --- */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: var(--footer-link-color);
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.footer-links a::before {
    content: '\f105'; /* FontAwesome right angle */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-right: 10px;
    color: var(--primary-color);
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateX(-5px);
}

.footer-links a:hover {
    color: var(--footer-link-hover-color);
    transform: translateX(5px);
}

.footer-links a:hover::before {
    opacity: 1;
    transform: translateX(0);
}

/* --- Newsletter Form --- */
.newsletter-form {
    position: relative;
    margin-top: 10px;
}

.newsletter-form p {
    margin-bottom: 20px;
}

.newsletter-form form {
    display: flex;
    border-radius: 50px;
    background-color: var(--footer-social-icon-bg);
    padding: 5px;
    border: 1px solid var(--footer-border-color);
}

.newsletter-form input {
    width: 100%;
    padding: 10px 15px;
    border: none;
    background-color: transparent;
    color: #fff;
    font-size: 14px;
    outline: none;
}

.newsletter-form input::placeholder {
    color: var(--footer-text-color);
    opacity: 0.7;
}

.newsletter-form button {
    flex-shrink: 0;
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.newsletter-form button:hover {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

/* --- Footer Bottom Section --- */
.footer-bottom {
    padding: 25px 0;
    border-top: 1px solid var(--footer-border-color);
    background-color: rgba(0, 0, 0, 0.2);
}

.footer-bottom .copyright {
    color: var(--footer-text-color);
    margin: 0;
    font-size: 14px;
}

.footer-bottom .text-lg-end {
    text-align: right;
}

.footer-bottom .fa-heart {
    color: var(--secondary-color);
    margin: 0 4px;
    animation: pulse 1.5s infinite ease-in-out;
}

/* --- Keyframes for animations --- */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
}

/* --- Responsive Styles --- */
@media (max-width: 991px) {
    .footer-top {
        padding: 60px 0 20px;
    }
    .footer-widget {
        text-align: center;
    }
    .about-widget .footer-logo,
    .social-links {
        margin-left: auto;
        margin-right: auto;
        justify-content: center;
    }
    .footer-title::after {
        left: 50%;
        transform: translateX(-50%);
    }
    .footer-links a {
        justify-content: center;
    }
    .footer-links a::before {
        display: none; /* Hide icon on mobile for cleaner look */
    }
    .footer-links a:hover {
        transform: none;
    }
    .footer-bottom .text-lg-end,
    .footer-bottom .col-lg-6 {
        text-align: center !important;
    }
    .footer-bottom .text-lg-end {
        margin-top: 10px;
    }
}
