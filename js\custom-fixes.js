// Custom fixes for JavaScript errors

document.addEventListener('DOMContentLoaded', function() {
    // Fix for main.js slider controller error
    // This script runs before main.js and creates dummy objects to prevent errors
    window.fixMainJsErrors = function() {
        // Create dummy Swiper objects if the elements don't exist
        if (!document.querySelector('.banner-slider-nine')) {
            window.bannerSliderNine = {
                controller: {
                    control: null
                }
            };
        }
        
        if (!document.querySelector('.rts-dynamic-thumb')) {
            window.rtsDynamicThumb = {
                controller: {
                    control: null
                }
            };
        }
        
        // Override the Swiper constructor to check for element existence
        const originalSwiper = window.Swiper;
        if (originalSwiper) {
            window.Swiper = function(selector, options) {
                // If the selector doesn't exist in the DOM, return a dummy object
                if (typeof selector === 'string' && !document.querySelector(selector)) {
                    return {
                        controller: {
                            control: null
                        },
                        on: function() { return this; },
                        update: function() { return this; }
                    };
                }
                
                // Otherwise, call the original Swiper constructor
                return new originalSwiper(selector, options);
            };
            
            // Copy prototype and properties
            window.Swiper.prototype = originalSwiper.prototype;
            Object.setPrototypeOf(window.Swiper, originalSwiper);
        }
    };
    
    // Run the fix
    window.fixMainJsErrors();
});