/**
 * Disabled version of ScrollTrigger.js
 * This file replaces the actual ScrollTrigger functionality with empty functions
 * to prevent any scroll-related issues.
 */

(function() {
    // Create a mock ScrollTrigger object
    window.ScrollTrigger = {
        // Mock all common methods
        create: function() { return {}; },
        refresh: function() {},
        update: function() {},
        getAll: function() { return []; },
        getById: function() { return null; },
        disable: function() {},
        enable: function() {},
        kill: function() {},
        config: function() {},
        register: function() {},
        defaults: function() {},
        
        // Add any other methods that might be called
        addEventListener: function() {},
        removeEventListener: function() {},
        batch: function() { return []; },
        isScrolling: function() { return false; },
        matchMedia: function() { return {}; },
        clearMatchMedia: function() {},
        observe: function() { return {}; },
        normalizeScroll: function() { return {}; },
        
        // Add version to avoid errors
        version: "3.11.2 (DISABLED)"
    };
    
    // Also disable GSAP ScrollTrigger plugin if it exists
    if (window.gsap && window.gsap.registerPlugin) {
        window.gsap.registerPlugin(window.ScrollTrigger);
    }
    
    console.log("ScrollTrigger functionality has been disabled to prevent scroll issues");
})();
