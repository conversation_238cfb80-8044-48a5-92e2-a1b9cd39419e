<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="images/fav.png">
    <title>Our Projects - Renewable Energy Solutions</title>
    <meta name="description" content="Explore our portfolio of successful renewable energy projects including solar installations, wind energy solutions, and energy storage systems.">

    <!-- fontawesome css -->
    <link rel="stylesheet" href="css/fontawesome-6.css">
    <!-- fontawesome css -->
    <link rel="stylesheet" href="css/swiper.css">
    <link rel="stylesheet" href="css/unicons.css">
    <link rel="stylesheet" href="css/metimenu.css">
    <link rel="stylesheet" href="css/animate.css">
    <!-- bootstrap css -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <!-- Custom css -->
    <link rel="stylesheet" href="css/style.css">
    <!-- Custom color palette -->
    <link rel="stylesheet" href="css/custom-palette.css">
    <!-- New color scheme -->
    <link rel="stylesheet" href="css/new-color-scheme.css">
    <!-- Improved layout -->
    <link rel="stylesheet" href="css/improved-layout.css">
    <!-- Mobile responsive styles -->
    <link rel="stylesheet" href="css/mobile-responsive.css">
    <!-- Improved footer styles -->
    <link rel="stylesheet" href="css/improved-footer.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom styles for project page -->
    <style>
        /* Bootstrap Dropdown Menu Styling */
        .dropdown-menu {
            border-radius: 10px;
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            margin-top: 10px;
            min-width: 250px;
            animation: fadeIn 0.3s ease;
        }

        .dropdown-item {
            padding: 10px 20px;
            color: var(--text-dark);
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover, .dropdown-item:focus {
            background-color: var(--primary-10);
            color: var(--primary-color);
            padding-left: 25px;
        }

        .dropdown-divider {
            margin: 8px 20px;
            border-color: var(--primary-10);
        }

        .dropdown-toggle::after {
            display: none;
        }

        .dropdown-toggle .fa-chevron-down {
            font-size: 12px;
            margin-left: 5px;
            transition: transform 0.3s ease;
        }

        .dropdown.show .dropdown-toggle .fa-chevron-down {
            transform: rotate(180deg);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Project page specific styles */
        /* Email overflow fix for mobile */
        .contact-info a {
            word-break: break-word;
            overflow-wrap: break-word;
            display: inline-block;
            max-width: 100%;
        }

        @media (max-width: 767px) {
            .contact-info a {
                font-size: 14px;
                line-height: 1.3;
            }
        }

        .project-categories-section,
        .all-projects-section {
            padding: 100px 0;
            background-color: var(--light-color);
        }

        .featured-projects-section {
            padding: 100px 0;
            background-color: var(--white-color);
        }

        .project-card {
            background-color: var(--white-color);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            transition: var(--transition-normal);
            height: 100%;
            margin-bottom: 30px;
        }

        .project-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-md);
        }

        .project-image {
            position: relative;
            overflow: hidden;
        }

        .project-image img {
            width: 100%;
            height: 220px;
            object-fit: cover;
            transition: var(--transition-normal);
        }

        .project-card:hover .project-image img {
            transform: scale(1.05);
        }

        .project-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.7));
            opacity: 0;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .project-card:hover .project-overlay {
            opacity: 1;
        }

        .project-link {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--white-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            transform: translateY(20px);
            opacity: 0;
            transition: var(--transition-slow);
        }

        .project-card:hover .project-link {
            transform: translateY(0);
            opacity: 1;
        }

        .project-content {
            padding: 25px;
        }

        .project-category {
            display: inline-block;
            color: var(--primary-color);
            font-weight: 500;
            margin-bottom: 10px;
        }

        .project-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 15px;
            color: var(--text-dark);
        }

        .project-content p {
            color: var(--text-light);
            margin-bottom: 20px;
        }

        .project-content .read-more {
            color: var(--secondary-color);
            font-weight: 600;
            display: inline-flex;
            align-items: center;
        }

        .project-content .read-more i {
            margin-left: 5px;
            transition: var(--transition-normal);
        }

        .project-content .read-more:hover i {
            transform: translateX(5px);
        }

        /* Responsive styles for project page */
        @media (max-width: 991px) {
            .project-categories-section,
            .featured-projects-section,
            .all-projects-section {
                padding: 70px 0;
            }

            .service-card {
                margin-bottom: 30px;
            }
        }

        @media (max-width: 767px) {
            .project-categories-section,
            .featured-projects-section,
            .all-projects-section {
                padding: 50px 0;
            }

            .section-header {
                flex-direction: column;
                align-items: center;
                text-align: center;
                margin-bottom: 30px;
            }

            .section-header .button-area {
                margin-top: 20px;
            }

            .service-card .service-image img {
                height: 200px;
            }
        }

        @media (max-width: 575px) {
            .service-card .service-image img {
                height: 180px;
            }

            .service-content {
                padding: 20px;
            }

            .service-title {
                font-size: 18px;
            }

            .contact-form-wrapper,
            .contact-info-wrapper {
                padding: 20px;
            }
        }
    </style>
</head>

<body>
    <!-- Header area start -->
    <header class="header-four header--sticky">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-2 col-md-3 col-6">
                    <div class="header-left">
                        <a href="index.html" class="logo-area">
                            <img src="images/logo-02.png" alt="logo">
                        </a>
                    </div>
                </div>
                <div class="col-lg-7 col-md-6 d-none d-md-block">
                    <div class="nav-area">
                        <!-- navigation area start -->
                        <div class="header-nav main-nav-one">
                            <nav>
                                <ul>
                                    <li>
                                        <a class="nav-link" href="index.html">Home</a>
                                    </li>
                                    <li>
                                        <a class="nav-link" href="aboutus.html">About</a>
                                    </li>
                                    <li>
                                        <a class="nav-link active" href="project.html">Project</a>
                                    </li>
                                    <li class="nav-item dropdown">
                                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            Services <i class="fa-solid fa-chevron-down"></i>
                                        </a>
                                        <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                                            <li><a class="dropdown-item" href="service.html">All Services</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="design-and-consultancy.html">Design & Consultancy</a></li>
                                            <li><a class="dropdown-item" href="solar-battery-ev.html">Solar, Battery & EV</a></li>
                                            <li><a class="dropdown-item" href="wind.html">Wind</a></li>
                                            <li><a class="dropdown-item" href="energy-audit-monitoring.html">Energy Audit & Monitoring</a></li>
                                            <li><a class="dropdown-item" href="solar-panel-servicing.html">Servicing</a></li>
                                        </ul>
                                    </li>
                                    <li><a class="nav-link" href="contact.html">Contact</a></li>
                                </ul>
                            </nav>
                        </div>
                        <!-- navigation area end -->
                    </div>
                </div>
                <div class="col-lg-3 col-md-3 col-6">
                    <div class="header-right">
                        <div class="action-button-menu">
                            <a href="contact.html" class="contact-btn">
                                <span class="btn-text">Let's Talk</span>
                                <span class="btn-icon"><i class="fa-solid fa-arrow-right"></i></span>
                            </a>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- Header area end -->

    <!-- New Mobile Navigation Bar -->
    <nav class="mobile-navbar d-md-none">
        <div class="mobile-nav-container">
            <a href="index.html" class="mobile-nav-item">
                <i class="fa-solid fa-home"></i>
                <span>Home</span>
            </a>
            <a href="aboutus.html" class="mobile-nav-item">
                <i class="fa-solid fa-info-circle"></i>
                <span>About</span>
            </a>
            <a href="project.html" class="mobile-nav-item active">
                <i class="fa-solid fa-project-diagram"></i>
                <span>Projects</span>
            </a>
            <a href="service.html" class="mobile-nav-item">
                <i class="fa-solid fa-solar-panel"></i>
                <span>Services</span>
            </a>
            <a href="contact.html" class="mobile-nav-item">
                <i class="fa-solid fa-envelope"></i>
                <span>Contact</span>
            </a>
        </div>
    </nav>

    <!-- Legacy elements kept for compatibility -->
    <div id="side-bar" class="side-bar d-none"></div>
    <div id="anywhere-home" class="d-none"></div>



    <!-- Project categories section start -->
    <section class="project-categories-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <div class="section-tag">Our Expertise</div>
                    <h2 class="title skew-up">Renewable Energy <span class="highlight-green">Solutions</span></h2>
                    <p class="section-description">We specialize in a wide range of renewable energy projects, from residential installations to large-scale commercial solutions.</p>
                </div>
            </div>
            <div class="row g-4 mt-4">
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card">
                        <div class="icon green">
                            <i class="fa-solid fa-solar-panel"></i>
                        </div>
                        <h4>Solar Energy</h4>
                        <p>Residential and commercial solar panel installations that maximize energy production and ROI.</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card">
                        <div class="icon orange">
                            <i class="fa-solid fa-wind"></i>
                        </div>
                        <h4>Wind Energy</h4>
                        <p>Wind turbine installations for properties with suitable wind resources and space requirements.</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card">
                        <div class="icon green">
                            <i class="fa-solid fa-car-battery"></i>
                        </div>
                        <h4>Energy Storage</h4>
                        <p>Battery storage solutions that ensure energy availability even when renewable sources are unavailable.</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card">
                        <div class="icon orange">
                            <i class="fa-solid fa-charging-station"></i>
                        </div>
                        <h4>EV Charging</h4>
                        <p>Electric vehicle charging infrastructure for homes, businesses, and public spaces.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Project categories section end -->

    <!-- Featured projects section start -->
    <section id="featured-projects" class="featured-projects-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="section-header">
                        <div class="title-area">
                            <div class="section-tag">Featured Projects</div>
                            <h2 class="title skew-up">Our <span class="highlight-green">Success Stories</span></h2>
                        </div>
                        <div class="button-area">
                            <a class="button-circle-text" href="#all-projects">
                                <i class="fa-solid fa-arrow-up-right"></i>
                                View All Projects
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row g-4">
                <!-- Project Card 1 -->
                <div class="col-lg-4 col-md-6">
                    <div class="service-card">
                        <div class="service-image">
                            <img src="images/monroe.webp" alt="Monroe Solar Farm">
                        </div>
                        <div class="service-content">
                            <div class="icon">
                                <img src="images/06.png" alt="icon">
                            </div>
                            <span class="service-category">Solar Farm</span>
                            <h4 class="service-title">Monroe Solar Farm</h4>
                            <p>A 14MW solar farm project that overcame unique terrain challenges to deliver sustainable energy to over 3,500 homes.</p>
                            <a href="monroe-solar-farm.html" class="read-more">View Project <i class="fa-regular fa-arrow-right"></i></a>
                        </div>
                    </div>
                </div>

                <!-- Project Card 2 -->
                <div class="col-lg-4 col-md-6">
                    <div class="service-card">
                        <div class="service-image">
                            <img src="images/follain.webp" alt="Follain Solar Project">
                        </div>
                        <div class="service-content">
                            <div class="icon">
                                <img src="images/06.png" alt="icon">
                            </div>
                            <span class="service-category">Commercial Solar</span>
                            <h4 class="service-title">Follain Project</h4>
                            <p>A 484 kWp solar installation for a commercial facility, reducing carbon emissions by 210 tons annually.</p>
                            <a href="follain.html" class="read-more">View Project <i class="fa-regular fa-arrow-right"></i></a>
                        </div>
                    </div>
                </div>

                <!-- Project Card 3 -->
                <div class="col-lg-4 col-md-6">
                    <div class="service-card">
                        <div class="service-image">
                            <img src="images/mahony.webp" alt="Denis Mahony Project">
                        </div>
                        <div class="service-content">
                            <div class="icon">
                                <img src="images/06.png" alt="icon">
                            </div>
                            <span class="service-category">Hybrid System</span>
                            <h4 class="service-title">Denis Mahony</h4>
                            <p>An integrated renewable energy system producing 95 MWh annually and covering 60% of the facility's energy needs.</p>
                            <a href="denis-mahony.html" class="read-more">View Project <i class="fa-regular fa-arrow-right"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Featured projects section end -->


    <!-- Contact section start -->
    <section class="contact-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <div class="contact-form-wrapper">
                        <div class="section-tag">Start Your Project</div>
                        <h2 class="title skew-up">Ready to Implement <span class="highlight-green">Renewable Energy?</span></h2>
                        <p>Fill out the form below and our team will get back to you within 24 hours to discuss your project requirements.</p>
                        <form class="contact-form" id="contactForm"
                              action="https://formspree.io/f/movddbga"
                              method="POST"
                              data-netlify="true"
                              name="contact">
                            <!-- Netlify form name (hidden) -->
                            <input type="hidden" name="form-name" value="contact">
                            <!-- Formspree redirect URL -->
                            <input type="hidden" name="_next" value="https://revolutionenergyindia.in/thank-you.html">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <input type="text" name="name" id="name" class="form-control" placeholder="Your Name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <input type="email" name="email" id="email" class="form-control" placeholder="Your Email" required>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <input type="tel" name="phone" id="phone" class="form-control" placeholder="Phone Number">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <textarea name="message" id="message" class="form-control" rows="4" placeholder="Project Details" required></textarea>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">Submit Request</button>
                                </div>
                                <div class="col-12 mt-3">
                                    <div id="form-message" class="alert" style="display: none;"></div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="contact-info-wrapper">
                        <div class="contact-info">
                            <div class="icon">
                                <i class="fa-solid fa-phone"></i>
                            </div>
                            <div>
                                <h5>Call Us</h5>
                                <p>For immediate assistance with your project</p>
                                <a href="javascript:void(0)" class="copy-number" data-number="9633126288">9633126288</a>
                            </div>
                        </div>
                        <div class="contact-info">
                            <div class="icon">
                                <i class="fa-solid fa-envelope"></i>
                            </div>
                            <div>
                                <h5>Email Us</h5>
                                <p>Send us your inquiries anytime</p>
                                <a href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>" target="_blank"><EMAIL></a>
                                <p><a href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>" target="_blank"><EMAIL></a></p>
                            </div>
                        </div>
                        <div class="contact-info">
                            <div class="icon">
                                <i class="fa-solid fa-location-dot"></i>
                            </div>
                            <div>
                                <h5>Visit Us</h5>
                                <p>Our headquarters location</p>
                                <address>251 Hilton Street, Berlin DE</address>
                            </div>
                        </div>
                        <div class="contact-map">
                            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2428.4090427798!2d13.3766!3d52.5163!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNTLCsDMwJzU5LjAiTiAxM8KwMjInMzUuOCJF!5e0!3m2!1sen!2sus!4v1625761214451!5m2!1sen!2sus" width="100%" height="200" style="border:0; border-radius: 15px;" allowfullscreen="" loading="lazy"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Contact section end -->

    <!-- Footer area start -->
    <!-- Footer start -->
    <footer class="footer">
        <div class="footer-top">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4 col-md-6">
                        <div class="footer-widget about-widget">
                            <img src="images/logo-02.png" alt="logo" class="footer-logo">
                            <p>REIN is dedicated to providing sustainable energy solutions that help reduce carbon footprints while saving costs. Our mission is to accelerate the transition to clean, renewable energy.</p>
                            <div class="social-links">
                                <a href="#" aria-label="Facebook"><i class="fa-brands fa-facebook-f"></i></a>
                                <a href="#" aria-label="Twitter"><i class="fa-brands fa-twitter"></i></a>
                                <a href="#" aria-label="LinkedIn"><i class="fa-brands fa-linkedin-in"></i></a>
                                <a href="#" aria-label="YouTube"><i class="fa-brands fa-youtube"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6">
                        <div class="footer-widget">
                            <h4 class="footer-title">Quick Links</h4>
                            <ul class="footer-links">
                                <li><a href="index.html">Home</a></li>
                                <li><a href="aboutus.html">About Us</a></li>
                                <li><a href="service.html">Services</a></li>
                                <li><a href="project.html">Projects</a></li>
                                <li><a href="contact.html">Contact</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="footer-widget">
                            <h4 class="footer-title">Our Services</h4>
                            <ul class="footer-links">
                                <li><a href="design-and-consultancy.html">Design & Consultancy</a></li>
                                <li><a href="solar-battery-ev.html">Solar, Battery & EV</a></li>
                                <li><a href="wind.html">Wind Energy Solutions</a></li>
                                <li><a href="energy-audit-monitoring.html">Energy Audit & Monitoring</a></li>
                                <li><a href="#">Maintenance & Support</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="footer-widget">
                            <h4 class="footer-title">Newsletter</h4>
                            <p>Subscribe to our newsletter to receive updates on the latest renewable energy trends and our services.</p>
                            <form class="newsletter-form">
                                <input type="email" placeholder="Your Email Address" aria-label="Email address for newsletter">
                                <button type="submit" aria-label="Subscribe to newsletter"><i class="fa-solid fa-paper-plane"></i></button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <p class="copyright">&copy; 2025 REIN. All Rights Reserved.</p>
                    </div>
                    <div class="col-lg-6 text-lg-end">
                        <p>Designed with <i class="fa-solid fa-heart" aria-hidden="true"></i> for a greener future</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <!-- Footer end -->
    
    <!-- Include the phone number copying script -->
    <script src="js/copy-phone.js"></script>

    <!-- Back to top button -->
    <a href="#" class="back-to-top">
        <i class="fa-solid fa-arrow-up"></i>
    </a>
    <!-- Back to top button end -->



    <!-- scripts start -->
    <script src="js/jquery-3.6.0.min.js"></script>
    <script src="js/jquery-ui.js"></script>
    <script src="js/swiper.js"></script>
    <script src="js/metisMenu.js"></script>
    <script src="js/aos.js"></script>
    <script src="js/waypoints.min.js"></script>
    <script src="js/counterup.js"></script>
    <script src="js/gsap.min.js"></script>
    <script src="js/scrollTrigger.min.js"></script>
    <script src="js/splitText.min.js"></script>
    <script src="js/jquery.nice-select.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/main.js"></script>

    <!-- Custom JS for copying phone numbers and form handling -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add click event listeners to all elements with class 'copy-number'
            const phoneNumbers = document.querySelectorAll('.copy-number');

            phoneNumbers.forEach(function(element) {
                element.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Get the phone number from the data attribute
                    const phoneNumber = this.getAttribute('data-number');

                    // Create a temporary input element
                    const tempInput = document.createElement('input');
                    tempInput.value = phoneNumber;
                    document.body.appendChild(tempInput);

                    // Select and copy the text
                    tempInput.select();
                    document.execCommand('copy');

                    // Remove the temporary element
                    document.body.removeChild(tempInput);

                    // Show feedback to the user
                    const originalText = this.textContent;
                    this.textContent = 'Copied!';

                    // Reset the text after a short delay
                    setTimeout(() => {
                        this.textContent = originalText;
                    }, 1500);
                });
            });

            // Contact form submission handling with Formspree
            const contactForm = document.getElementById('contactForm');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    // We don't prevent default form submission since Formspree handles it
                    // But we can add some visual feedback

                    const formMessage = document.getElementById('form-message');
                    const submitButton = contactForm.querySelector('button[type="submit"]');

                    // Disable submit button and show loading state
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> Sending...';

                    // Show a temporary sending message
                    formMessage.style.display = 'block';
                    formMessage.className = 'alert alert-info';
                    formMessage.innerHTML = '<i class="fa-solid fa-paper-plane"></i> Sending your message...';

                    // The form will be submitted normally to Formspree
                    // We'll add a small delay to show the sending message
                    setTimeout(() => {
                        // The page will redirect to Formspree's thank you page
                        // or stay on the page if there's an error
                    }, 1000);
                });
            }

            // New Mobile Navbar Functionality - Fixed Dropdown
            const mobileDropdown = document.querySelector('.mobile-dropdown');
            const mobileDropdownToggle = document.querySelector('.mobile-dropdown-toggle');

            if (mobileDropdownToggle && mobileDropdown) {
                // Improved click handler for the dropdown toggle
                mobileDropdownToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Toggle the active class
                    mobileDropdown.classList.toggle('active');

                    // Force display block on the dropdown menu when active
                    const dropdownMenu = mobileDropdown.querySelector('.mobile-dropdown-menu');
                    if (dropdownMenu) {
                        if (mobileDropdown.classList.contains('active')) {
                            dropdownMenu.style.display = 'block';
                        } else {
                            // Use setTimeout to allow for animation
                            setTimeout(() => {
                                if (!mobileDropdown.classList.contains('active')) {
                                    dropdownMenu.style.display = 'none';
                                }
                            }, 300);
                        }
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (mobileDropdown && !mobileDropdown.contains(e.target)) {
                        mobileDropdown.classList.remove('active');

                        // Hide the dropdown menu
                        const dropdownMenu = mobileDropdown.querySelector('.mobile-dropdown-menu');
                        if (dropdownMenu) {
                            setTimeout(() => {
                                if (!mobileDropdown.classList.contains('active')) {
                                    dropdownMenu.style.display = 'none';
                                }
                            }, 300);
                        }
                    }
                });

                // Close dropdown when scrolling
                window.addEventListener('scroll', function() {
                    if (mobileDropdown) {
                        mobileDropdown.classList.remove('active');

                        // Hide the dropdown menu
                        const dropdownMenu = mobileDropdown.querySelector('.mobile-dropdown-menu');
                        if (dropdownMenu) {
                            setTimeout(() => {
                                if (!mobileDropdown.classList.contains('active')) {
                                    dropdownMenu.style.display = 'none';
                                }
                            }, 300);
                        }
                    }
                });
            }

            // Set active class for current page in mobile navbar
            const currentPath = window.location.pathname;
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');

            mobileNavItems.forEach(item => {
                const href = item.getAttribute('href');
                if (href && currentPath.includes(href)) {
                    // Remove active class from all items
                    mobileNavItems.forEach(i => i.classList.remove('active'));
                    // Add active class to current item
                    item.classList.add('active');
                }
            });

            // Desktop dropdown functionality
            const desktopDropdowns = document.querySelectorAll('.header-nav .has-dropdown');
            desktopDropdowns.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.classList.add('active');
                    $(this).find('.submenu').stop().slideDown(300);
                });

                item.addEventListener('mouseleave', function() {
                    this.classList.remove('active');
                    $(this).find('.submenu').stop().slideUp(300);
                });

                // Add click functionality for touch devices
                const dropdownLink = item.querySelector('a');
                dropdownLink.addEventListener('click', function(e) {
                    if (window.innerWidth > 991) {
                        // Only for desktop/tablet
                        if (!item.classList.contains('active')) {
                            e.preventDefault();
                            item.classList.add('active');
                            $(item).find('.submenu').stop().slideDown(300);
                        }
                    }
                });
            });
        });
    </script>
    <!-- scripts end -->
</body>

</html>